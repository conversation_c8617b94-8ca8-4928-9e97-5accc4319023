      <!-- Content -->
      <div class="bg-white shadow rounded-lg">
        <!-- Cases Table -->
        <div v-if="activeTab === 'cases'" class="overflow-x-auto">
          <div v-if="(cases?.length??0) === 0" class="text-center py-8">
            <p class="text-gray-500 text-lg">No cases available</p>
            <div class="flex justify-center space-x-4 mt-4">
              <button
                @click="createNewCase"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Create New Case
              </button>
              <button
                @click="goToOcrImport"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                Import from OCR
              </button>
            </div>
          </div>
          <table v-else class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Case ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Case Type</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prescriber</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Drug Family</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="caseItem in cases" :key="caseItem.caseId">
                <td class="px-6 py-4 whitespace-nowrap">
                  <router-link :to="{ name: 'case-details', params: { caseId: caseItem.caseId } }" class="text-indigo-600 hover:text-indigo-900">
                    {{ caseItem.caseId }}
                  </router-link>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">{{ caseItem.caseType }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ caseItem.enrollmentStatus }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {{ caseItem.patient?.firstName || '' }} {{ caseItem.patient?.middleName || '' }} {{ caseItem.patient?.lastName || '' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {{ caseItem.prescriber?.firstName || '' }} {{ caseItem.prescriber?.lastName || '' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">{{ caseItem.drugFamily?.name || '' }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(caseItem.createdDate) }}</td>
                <td class="px-6 py-4 flex whitespace-nowrap text-sm font-medium">
                  <router-link
                    :to="{ name: 'case-details', params: { caseId: caseItem.caseId } }"
                    class="text-indigo-600 hover:text-indigo-900 mr-3"
                    title="Edit Case"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                      <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" />
                    </svg>
                  </router-link>
                  <button
                    @click="deleteCase(caseItem.caseId)"
                    class="text-red-600 hover:text-red-900"
                    title="Delete Case"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination for Cases -->
          <Pagination
            v-if="cases.length > 0"
            :current-page="paginationState.cases.currentPage"
            :total-pages="paginationState.cases.totalPages"
            :total-items="paginationState.cases.totalItems"
            :page-size="paginationState.cases.pageSize"
            @page-change="handlePageChange('cases', $event)"
            @page-size-change="handlePageSizeChange('cases', $event)"
          />
        </div>

        <!-- Patients Table -->
        <div v-if="activeTab === 'patients'" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date of Birth</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Zip Code</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="patient in patients" :key="patient.id">
                <td class="px-6 py-4 whitespace-nowrap">
                  <router-link :to="{ name: 'patient-details', params: { patientId: patient.id } }" class="text-indigo-600 hover:text-indigo-900">
                    {{ patient.firstName }} {{ patient.middleName ? patient.middleName + ' ' : '' }}{{ patient.lastName }}
                  </router-link>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(patient.dateOfBirth) }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ patient.gender }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ patient.zipCode }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button class="text-indigo-600 hover:text-indigo-900 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                      <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" />
                    </svg>
                  </button>
                  <button class="text-red-600 hover:text-red-900">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination for Patients -->
          <Pagination
            v-if="patients.length > 0"
            :current-page="paginationState.patients.currentPage"
            :total-pages="paginationState.patients.totalPages"
            :total-items="paginationState.patients.totalItems"
            :page-size="paginationState.patients.pageSize"
            @page-change="handlePageChange('patients', $event)"
            @page-size-change="handlePageSizeChange('patients', $event)"
          />
        </div>

        <!-- Prescribers Table -->
        <div v-if="activeTab === 'prescribers'" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fax</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="prescriber in prescribers" :key="prescriber.id">
                <td class="px-6 py-4 whitespace-nowrap">
                  <router-link :to="{ name: 'prescriber-details', params: { prescriberId: prescriber.id } }" class="text-indigo-600 hover:text-indigo-900">
                    {{ prescriber.firstName }} {{ prescriber.lastName }}
                  </router-link>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">{{ prescriber.address }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ prescriber.fax }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button class="text-indigo-600 hover:text-indigo-900 mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" />
                      <path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" />
                    </svg>
                  </button>
                  <button class="text-red-600 hover:text-red-900">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination for Prescribers -->
          <Pagination
            v-if="prescribers.length > 0"
            :current-page="paginationState.prescribers.currentPage"
            :total-pages="paginationState.prescribers.totalPages"
            :total-items="paginationState.prescribers.totalItems"
            :page-size="paginationState.prescribers.pageSize"
            @page-change="handlePageChange('prescribers', $event)"
            @page-size-change="handlePageSizeChange('prescribers', $event)"
          />
        </div>

        <!-- Agent List -->
        <div v-if="activeTab === 'agents'" class="overflow-x-auto">
          <AgentList :show-auto-assign-modal="showAgentListAutoAssignModal" @close-auto-assign-modal="closeAgentListAutoAssignModal" />
        </div>
      </div>
    </div>
  </div>
</template>
