<script setup lang="ts">
import { ref, onMounted, watch, reactive } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import Pagination from './utility/Pagination.vue';
import AgentList from './AgentList.vue';
import { useOrganization } from '../contexts/OrganizationContext';

interface Patient {
  id: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  zipCode: string;
  age?: number;
  createdDate: string;
  updatedDate: string;
}

interface Prescriber {
  id: string;
  firstName: string;
  lastName: string;
  address: string;
  fax: string;
  createdDate: string;
  updatedDate: string;
}

interface Case {
  caseId: string;
  caseType: string;
  enrollmentStatus: string;
  patient: Patient;
  prescriber: Prescriber;
  drugFamily: {
    name: string;
  };
  createdDate: string;
}

interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface Agent {
  id: string;
  name: string;
  organization: string;
  skills: string;
}

interface TabPaginationState {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
}

interface CasesFetchParams {
  page: number;
  pageSize: number;
  organizationId?: string;
  caseProgressStatus?: string;
  search?: string;
  patientName?: string;
  prescriberName?: string;
  drugFamilyName?: string;
}

const router = useRouter();
const activeTab = ref('cases');
const activeProgressStage = ref('new');
const showSearchForm = ref(false);
const showAgentListAutoAssignModal = ref(false);
const patients = ref<Patient[]>([]);
const prescribers = ref<Prescriber[]>([]);
const cases = ref<Case[]>([]);
const agents = ref<Agent[]>([]);

// Pagination state for each tab
const paginationState = reactive({
  cases: {
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 10
  },
  patients: {
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 10
  },
  prescribers: {
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 10
  },
  agents: {
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 10
  }
});

const tabs = [
  { id: 'cases', name: 'Cases' },
  { id: 'patients', name: 'Patients' },
  { id: 'prescribers', name: 'Prescribers' },
  { id: 'agents', name: 'Agents' },
];

// Search form data for different tabs
const caseSearchFormData = reactive({
  search: '',
  patientName: '',
  prescriberName: '',
  drugFamilyName: ''
});

const patientSearchFormData = reactive({
  firstName: '',
  lastName: '',
  dateOfBirth: '',
  gender: '',
  zipCode: ''
});

const prescriberSearchFormData = reactive({
  firstName: '',
  lastName: '',
  address: '',
  fax: ''
});

const agentSearchFormData = reactive({
  name: '',
  organization: '',
  skills: '',
});

// Search form labels
const caseSearchFormLabels = {
  search: 'General Search',
  patientName: 'Patient Name',
  prescriberName: 'Prescriber Name',
  drugFamilyName: 'Drug Family Name'
};

const patientSearchFormLabels = {
  firstName: 'First Name',
  lastName: 'Last Name',
  dateOfBirth: 'Date of Birth',
  gender: 'Gender',
  zipCode: 'ZIP Code'
};

const prescriberSearchFormLabels = {
  firstName: 'First Name',
  lastName: 'Last Name',
  address: 'Address',
  fax: 'Fax'
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString();
};

const deleteCase = async (caseId: string) => {
  const confirmed = confirm(`Are you sure you want to delete case ${caseId}? This action cannot be undone.`);
  if (!confirmed) return;

  try {
    await axios.delete(`http://localhost:3000/api/patient-cases/${caseId}`);
    alert('Case deleted successfully');
    if (activeTab.value === 'cases') {
      fetchCasesByStage();
    }
  } catch (error: any) {
    console.error('Error deleting case:', error);
    if (error.response?.status === 404) {
      alert('Case not found. It may have already been deleted.');
    } else {
      alert('Failed to delete case. Please try again.');
    }
  }
};

const toggleSearchForm = () => {
  showSearchForm.value = !showSearchForm.value;
};

const submitCaseSearch = async () => {
  try {
    const orgId = getOrganizationId();
    const params: CasesFetchParams = {
      page: paginationState.cases.currentPage,
      pageSize: paginationState.cases.pageSize,
      ...caseSearchFormData,
      ...(orgId && { organizationId: orgId })
    };
    const response = await axios.get('http://localhost:3000/api/patient-cases', { params });
    cases.value = response.data.data;
    paginationState.cases.totalItems = response.data.meta.total;
    paginationState.cases.totalPages = response.data.meta.totalPages;
    activeTab.value = 'cases';
  } catch (error) {
    console.error('Error searching cases:', error);
  }
};

const clearCaseSearch = () => {
  Object.keys(caseSearchFormData).forEach(key => {
    (caseSearchFormData as any)[key] = '';
  });
  fetchCasesByStage();
};

const submitPatientSearch = async () => {
  try {
    const orgId = getOrganizationId();
    const params = {
      ...patientSearchFormData,
      ...(orgId && { organizationId: orgId })
    };
    const response = await axios.get('http://localhost:3000/api/patient', { params });
    patients.value = response.data.data;
    paginationState.patients.totalItems = response.data.meta.total;
    paginationState.patients.totalPages = response.data.meta.totalPages;
    activeTab.value = 'patients';
  } catch (error) {
    console.error('Error searching patients:', error);
  }
};

const clearPatientSearch = () => {
  Object.keys(patientSearchFormData).forEach(key => {
    (patientSearchFormData as any)[key] = '';
  });
  fetchPatients();
};

const submitPrescriberSearch = async () => {
  try {
    const orgId = getOrganizationId();
    const params = {
      ...prescriberSearchFormData,
      ...(orgId && { organizationId: orgId })
    };
    const response = await axios.get('http://localhost:3000/api/prescriber', { params });
    prescribers.value = response.data.data;
    paginationState.prescribers.totalItems = response.data.meta.total;
    paginationState.prescribers.totalPages = response.data.meta.totalPages;
    activeTab.value = 'prescribers';
  } catch (error) {
    console.error('Error searching prescribers:', error);
  }
};

const clearPrescriberSearch = () => {
  Object.keys(prescriberSearchFormData).forEach(key => {
    (prescriberSearchFormData as any)[key] = '';
  });
  fetchPrescribers();
};

const submitAgentSearch = async () => {
  try {
    const orgId = getOrganizationId();
    const response = await axios.get('/api/agents', {
      params: {
        name: agentSearchFormData.name,
        organization: agentSearchFormData.organization,
        skills: agentSearchFormData.skills,
        ...(orgId && { organizationId: orgId })
      },
    });
    router.push({
      path: '/agents',
      query: {
        name: agentSearchFormData.name,
        organization: agentSearchFormData.organization,
        skills: agentSearchFormData.skills,
        ...(orgId && { organizationId: orgId })
      },
    });
  } catch (error) {
    console.error('Error searching agents:', error);
  }
};

const clearAgentSearch = () => {
  agentSearchFormData.name = '';
  agentSearchFormData.organization = '';
  agentSearchFormData.skills = '';
};

const createNewCase = () => {
  router.push('/case/new');
};

const goToOcrImport = () => {
  router.push('/ocr-import');
};

const createNewPatient = () => {
  router.push('/patient/new');
};

const createNewPrescriber = () => {
  router.push('/prescriber/new');
};

const createNewAgent = () => {
  router.push('/agents/create');
};

const goToAgents = () => {
  router.push('/agents');
};

const openAgentListAutoAssignModal = () => {
  showAgentListAutoAssignModal.value = true;
};

const closeAgentListAutoAssignModal = () => {
  showAgentListAutoAssignModal.value = false;
};

const manualAssignAgent = () => {
  alert('Manual Assign Agent functionality coming soon!');
};

const fetchPatients = async (page = 1, pageSize = 10) => {
  try {
    paginationState.patients.currentPage = page;
    paginationState.patients.pageSize = pageSize;
    const orgId = getOrganizationId();
    const response = await axios.get('http://localhost:3000/api/patient', {
      params: {
        page,
        pageSize,
        ...(orgId && { organizationId: orgId })
      }
    });
    patients.value = response.data.data;
    paginationState.patients.totalItems = response.data.meta.total;
    paginationState.patients.totalPages = response.data.meta.totalPages;
  } catch (error) {
    console.error('Error fetching patients:', error);
    patients.value = [];
  }
};

const fetchPrescribers = async (page = 1, pageSize = 10) => {
  try {
    paginationState.prescribers.currentPage = page;
    paginationState.prescribers.pageSize = pageSize;
    const orgId = getOrganizationId();
    const response = await axios.get('http://localhost:3000/api/prescriber', {
      params: {
        page,
        pageSize,
        ...(orgId && { organizationId: orgId })
      }
    });
    prescribers.value = response.data.data;
    paginationState.prescribers.totalItems = response.data.meta.total;
    paginationState.prescribers.totalPages = response.data.meta.totalPages;
  } catch (error) {
    console.error('Error fetching prescribers:', error);
    prescribers.value = [];
  }
};

const fetchCasesByStage = async (page = 1, pageSize = 10) => {
  try {
    paginationState.cases.currentPage = page;
    paginationState.cases.pageSize = pageSize;
    const orgId = getOrganizationId();
    
    console.log('Fetching cases for organization:', orgId);

    const params: CasesFetchParams = {
      page,
      pageSize,
      ...(orgId && { organizationId: orgId })
    };
    
    if (activeProgressStage.value !== 'ALL') {
      params.caseProgressStatus = activeProgressStage.value;
    }
    
    const response = await axios.get('http://localhost:3000/api/patient-cases', { params });
    cases.value = response.data.data;
    paginationState.cases.totalItems = response.data.meta.total;
    paginationState.cases.totalPages = response.data.meta.totalPages;
  } catch (error) {
    console.error('Error fetching cases:', error);
    cases.value = [];
  }
};

const fetchAgents = async (page = 1, pageSize = 10) => {
  try {
    paginationState.agents.currentPage = page;
    paginationState.agents.pageSize = pageSize;
    const orgId = getOrganizationId();
    const response = await axios.get('/api/agents', {
      params: {
        page,
        pageSize,
        ...(orgId && { organizationId: orgId })
      }
    });
    agents.value = response.data.data;
    paginationState.agents.totalItems = response.data.meta.total;
    paginationState.agents.totalPages = response.data.meta.totalPages;
  } catch (error) {
    console.error('Error fetching agents:', error);
    agents.value = [];
  }
};

const progressStages = [
  { id: 'ALL', name: 'All Cases' },
  { id: 'STAGE_1', name: 'Intake' },
  { id: 'STAGE_2', name: 'BI Coverage' },
  { id: 'STAGE_3', name: 'RX Transfer' },
  { id: 'COMPLETED', name: 'Closed' },
  { id: 'ON_HOLD', name: 'On Hold' },
  { id: 'CANCELLED', name: 'Cancelled' }
];

const handlePageChange = (tab: string, page: number) => {
  if (tab === 'cases') {
    paginationState.cases.currentPage = page;
    fetchCasesByStage();
  } else if (tab === 'patients') {
    paginationState.patients.currentPage = page;
    fetchPatients();
  } else if (tab === 'prescribers') {
    paginationState.prescribers.currentPage = page;
    fetchPrescribers();
  }
};

const handlePageSizeChange = (tab: string, pageSize: number) => {
  if (tab === 'cases') {
    paginationState.cases.pageSize = pageSize;
    paginationState.cases.currentPage = 1;
    fetchCasesByStage();
  } else if (tab === 'patients') {
    paginationState.patients.pageSize = pageSize;
    paginationState.patients.currentPage = 1;
    fetchPatients();
  } else if (tab === 'prescribers') {
    paginationState.prescribers.pageSize = pageSize;
    paginationState.prescribers.currentPage = 1;
    fetchPrescribers();
  }
};

watch(activeTab, (newTab) => {
  showSearchForm.value = false;

  switch (newTab) {
    case 'cases':
      fetchCasesByStage();
      break;
    case 'patients':
      fetchPatients();
      break;
    case 'prescribers':
      fetchPrescribers();
      break;
    case 'agents':
      fetchAgents(); // FIXED: Now fetches agents instead of doing nothing
      break;
  }
});

watch(activeProgressStage, () => {
  if (activeTab.value === 'cases') {
    paginationState.cases.currentPage = 1;
    fetchCasesByStage();
  }
});

const { getOrganization, getOrganizationId, setOrganization } = useOrganization();

watch(getOrganization, () => {
  console.log('Organization changed, refetching data for:', getOrganizationId());
  switch (activeTab.value) {
    case 'cases':
      fetchCasesByStage();
      break;
    case 'patients':
      fetchPatients();
      break;
    case 'prescribers':
      fetchPrescribers();
      break;
    case 'agents':
      fetchAgents();
      break;
  }
});

onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const tabParam = urlParams.get('tab');
  if (tabParam && ['cases', 'patients', 'prescribers', 'agents'].includes(tabParam)) {
    activeTab.value = tabParam;
    if (tabParam === 'cases') {
      activeProgressStage.value = 'ALL';
    }
  }

  const token = localStorage.getItem('token');
  if (!token) {
    router.push('/login');
    return;
  }

  const storedOrg = localStorage.getItem('selectedOrganization');
  if (storedOrg) {
    const organization = JSON.parse(storedOrg);
    setOrganization(organization);
  }

  switch (activeTab.value) {
    case 'cases':
      fetchCasesByStage();
      break;
    case 'patients':
      fetchPatients();
      break;
    case 'prescribers':
      fetchPrescribers();
      break;
    case 'agents':
      fetchAgents(); // FIXED: Now fetches agents instead of doing nothing
      break;
  }
});
</script>

<style scoped>
/* Styles are handled by Tailwind classes */
</style>
