<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Navigation Bar -->
    <nav class="bg-white shadow-lg">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex">
            <!-- Logo/Brand -->
            <div class="flex-shrink-0 flex items-center">
              <h1 class="text-xl font-bold text-gray-800"></h1>
            </div>

            <!-- Navigation Links -->
            <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
              <button
                v-for="tab in tabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="[
                  activeTab === tab.id
                    ? 'border-indigo-500 text-gray-900'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                  'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium'
                ]"
              >
                {{ tab.name }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Mobile menu -->
      <div class="sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              activeTab === tab.id
                ? 'bg-indigo-50 border-indigo-500 text-indigo-700'
                : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700',
              'block pl-3 pr-4 py-2 border-l-4 text-base font-medium'
            ]"
          >
            {{ tab.name }}
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Progress Status Tabs -->
      <div v-if="activeTab === 'cases'" class="mb-4 flex justify-between bg-white shadow rounded-lg p-4">
        <div class="flex space-x-2 border-gray-200">
          <button
            v-for="stage in progressStages"
            :key="stage.id"
            @click="activeProgressStage = stage.id; fetchCasesByStage()"
            :class="[
              activeProgressStage === stage.id
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
              'py-2 px-4 text-sm font-medium border-b-2'
            ]"
          >
            {{ stage.name }}
          </button>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="toggleSearchForm"
            class="text-gray-500 hover:text-gray-700"
          >
            🔍 Search
          </button>
          <button
            @click="goToOcrImport"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            Import from OCR
          </button>
          <button
            @click="createNewCase"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Create New Case
          </button>
        </div>
      </div>

      <!-- Patient Tab Actions -->
      <div v-if="activeTab === 'patients'" class="mb-4 flex justify-between bg-white shadow rounded-lg p-4">
        <div class="flex items-center space-x-4">
          <button
            @click="toggleSearchForm"
            class="text-gray-500 hover:text-gray-700"
          >
            🔍 Search
          </button>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="createNewPatient"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Create New Patient
          </button>
        </div>
      </div>

      <!-- Prescriber Tab Actions -->
      <div v-if="activeTab === 'prescribers'" class="mb-4 flex justify-between bg-white shadow rounded-lg p-4">
        <div class="flex items-center space-x-4">
          <button
            @click="toggleSearchForm"
            class="text-gray-500 hover:text-gray-700"
          >
            🔍 Search
          </button>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="createNewPrescriber"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Create New Prescriber
          </button>
        </div>
      </div>

      <!-- Agent Tab Actions -->
      <div v-if="activeTab === 'agents'" class="mb-4 flex justify-between bg-white shadow rounded-lg p-4">
        <div class="flex items-center space-x-4">
          <button
            @click="toggleSearchForm"
            class="text-gray-500 hover:text-gray-700"
          >
            🔍 Search
          </button>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="createNewAgent"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Create New Agent
          </button>
          <button
            @click="openAgentListAutoAssignModal"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            Auto Assign Agent
          </button>
          <button
            @click="manualAssignAgent"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700"
          >
            Manual Assign Agent
          </button>
          <button
            @click="goToAgents"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            View All Agents
          </button>
        </div>
      </div>

      <!-- Search Forms -->
      <div v-if="showSearchForm" class="mb-6">
        <div class="bg-white shadow rounded-lg p-6">
          <!-- Cases Search Form -->
          <form v-if="activeTab === 'cases'" @submit.prevent="submitCaseSearch" class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Search Cases</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
              <div v-for="(label, key) in caseSearchFormLabels" :key="key">
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ label }}</label>
                <input
                  v-model="caseSearchFormData[key]"
                  type="text"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div class="pt-4 flex space-x-4">
              <button
                type="submit"
                class="px-6 py-2 bg-blue-600 text-white font-semibold rounded hover:bg-blue-700 transition duration-150"
              >
                🔍 Search Cases
              </button>
              <button
                type="button"
                @click="clearCaseSearch"
                class="px-6 py-2 bg-gray-600 text-white font-semibold rounded hover:bg-gray-700 transition duration-150"
              >
                Clear
              </button>
            </div>
          </form>

          <!-- Patients Search Form -->
          <form v-if="activeTab === 'patients'" @submit.prevent="submitPatientSearch" class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Search Patients</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
              <div v-for="(label, key) in patientSearchFormLabels" :key="key">
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ label }}</label>
                <input
                  v-model="patientSearchFormData[key]"
                  :type="key === 'dateOfBirth' ? 'date' : 'text'"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div class="pt-4 flex space-x-4">
              <button
                type="submit"
                class="px-6 py-2 bg-blue-600 text-white font-semibold rounded hover:bg-blue-700 transition duration-150"
              >
                🔍 Search Patients
              </button>
              <button
                type="button"
                @click="clearPatientSearch"
                class="px-6 py-2 bg-gray-600 text-white font-semibold rounded hover:bg-gray-700 transition duration-150"
              >
                Clear
              </button>
            </div>
          </form>

          <!-- Prescribers Search Form -->
          <form v-if="activeTab === 'prescribers'" @submit.prevent="submitPrescriberSearch" class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Search Prescribers</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
              <div v-for="(label, key) in prescriberSearchFormLabels" :key="key">
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ label }}</label>
                <input
                  v-model="prescriberSearchFormData[key]"
                  type="text"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div class="pt-4 flex space-x-4">
              <button
                type="submit"
                class="px-6 py-2 bg-blue-600 text-white font-semibold rounded hover:bg-blue-700 transition duration-150"
              >
                🔍 Search Prescribers
              </button>
              <button
                type="button"
                @click="clearPrescriberSearch"
                class="px-6 py-2 bg-gray-600 text-white font-semibold rounded hover:bg-gray-700 transition duration-150"
              >
                Clear
              </button>
            </div>
          </form>

          <!-- Agent Search Form -->
          <form v-if="activeTab === 'agents'" @submit.prevent="submitAgentSearch" class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Search Agents</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input
                  v-model="agentSearchFormData.name"
                  type="text"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Organization</label>
                <input
                  v-model="agentSearchFormData.organization"
                  type="text"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Skills</label>
                <input
                  v-model="agentSearchFormData.skills"
                  type="text"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div class="pt-4 flex space-x-4">
              <button
                type="submit"
                class="px-6 py-2 bg-blue-600 text-white font-semibold rounded hover:bg-blue-700 transition duration-150"
              >
                🔍 Search Agents
              </button>
              <button
                type="button"
                @click="clearAgentSearch"
                class="px-6 py-2 bg-gray-600 text-white font-semibold rounded hover:bg-gray-700 transition duration-150"
              >
                Clear
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Content -->
      <div class="bg-white shadow rounded-lg">
        <!-- Agent List -->
        <div v-if="activeTab === 'agents'" class="overflow-x-auto">
          <AgentList :show-auto-assign-modal="showAgentListAutoAssignModal" @close-auto-assign-modal="closeAgentListAutoAssignModal" />
        </div>
      </div>
    </div>
  </div>
</template>
